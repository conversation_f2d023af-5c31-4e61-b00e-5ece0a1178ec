#!/usr/bin/env python3
"""
Backend API testing script
Tests all major endpoints with authentication
"""

import requests
import json
from datetime import datetime

BASE_URL = "http://localhost:8000"

def test_backend():
    print("🧪 Testing Asset Registry Backend API")
    print("=" * 50)
    
    # Test 1: Health check
    print("\n1. Testing health endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return
    
    # Test 2: Authentication
    print("\n2. Testing authentication...")
    try:
        auth_data = {
            "username": "admin",
            "password": "admin123!@#"
        }
        response = requests.post(
            f"{BASE_URL}/api/auth/token",
            data=auth_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data["access_token"]
            print(f"   ✅ Login successful")
            print(f"   Token type: {token_data['token_type']}")
        else:
            print(f"   ❌ Login failed: {response.json()}")
            return
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return
    
    # Set up headers for authenticated requests
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    # Test 3: Dashboard stats
    print("\n3. Testing dashboard stats...")
    try:
        response = requests.get(f"{BASE_URL}/api/dashboard/stats", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            stats = response.json()
            print(f"   ✅ Total assets: {stats.get('total_assets', 'N/A')}")
            print(f"   ✅ Active assets: {stats.get('active_assets', 'N/A')}")
            print(f"   ✅ Pending verifications: {stats.get('pending_verifications', 'N/A')}")
        else:
            print(f"   ❌ Error: {response.json()}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 4: Get assets
    print("\n4. Testing assets endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/api/assets/", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            assets = response.json()
            print(f"   ✅ Retrieved {len(assets)} assets")
            if assets:
                print(f"   Sample asset: {assets[0]['asset_id']} - {assets[0]['description']}")
        else:
            print(f"   ❌ Error: {response.json()}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 5: Create new asset
    print("\n5. Testing asset creation...")
    try:
        new_asset = {
            "asset_id": f"TEST-{datetime.now().strftime('%Y%m%d%H%M%S')}",
            "description": "Test Asset for API Testing",
            "location": "Building A - Floor 1",
            "custodian": "Test User",
            "barcode": f"BC{datetime.now().strftime('%Y%m%d%H%M%S')}",
            "purchase_date": datetime.now().isoformat(),
            "depreciation_class": "equipment",
            "purchase_cost": 1000.0,
            "useful_life_years": 5,
            "status": "active",
            "category": "Desktop",
            "notes": "Created via API test"
        }
        
        response = requests.post(f"{BASE_URL}/api/assets/", headers=headers, json=new_asset)
        print(f"   Status: {response.status_code}")
        if response.status_code == 201:
            created_asset = response.json()
            print(f"   ✅ Asset created: {created_asset['asset_id']}")
            test_asset_id = created_asset['asset_id']
        else:
            print(f"   ❌ Error: {response.json()}")
            test_asset_id = None
    except Exception as e:
        print(f"   ❌ Error: {e}")
        test_asset_id = None
    
    # Test 6: Update asset (if creation was successful)
    if test_asset_id:
        print("\n6. Testing asset update...")
        try:
            update_data = {
                "description": "Updated Test Asset",
                "status": "maintenance"
            }
            response = requests.put(f"{BASE_URL}/api/assets/{test_asset_id}", headers=headers, json=update_data)
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print(f"   ✅ Asset updated successfully")
            else:
                print(f"   ❌ Error: {response.json()}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Test 7: Delete asset
        print("\n7. Testing asset deletion...")
        try:
            response = requests.delete(f"{BASE_URL}/api/assets/{test_asset_id}", headers=headers)
            print(f"   Status: {response.status_code}")
            if response.status_code == 204:
                print(f"   ✅ Asset deleted successfully")
            else:
                print(f"   ❌ Error: {response.json()}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    # Test 8: Recent audit logs
    print("\n8. Testing audit logs...")
    try:
        response = requests.get(f"{BASE_URL}/api/audit/recent", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            logs = response.json()
            print(f"   ✅ Retrieved {len(logs)} audit logs")
        else:
            print(f"   ❌ Error: {response.json()}")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Backend API testing completed!")
    print("\nNext steps:")
    print("1. Frontend should now be able to connect to backend")
    print("2. Login with admin/admin123!@# in the frontend")
    print("3. Test all CRUD operations through the UI")

if __name__ == "__main__":
    test_backend()
