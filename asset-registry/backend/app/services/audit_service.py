from datetime import datetime
from typing import Optional, Dict, Any
from app.models.audit import AuditTrail, AuditAction
from bson import ObjectId

async def log_audit_event(
    db,
    user_id: Optional[str],
    username: str,
    action: AuditAction,
    resource_type: str,
    resource_id: Optional[str] = None,
    details: Dict[str, Any] = None,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None
):
    """Log an audit event to the database"""
    
    audit_record = {
        "_id": ObjectId(),
        "user_id": user_id,
        "username": username,
        "action": action.value,
        "resource_type": resource_type,
        "resource_id": resource_id,
        "details": details or {},
        "ip_address": ip_address,
        "user_agent": user_agent,
        "timestamp": datetime.utcnow()
    }
    
    try:
        await db.audit_trail.insert_one(audit_record)
    except Exception as e:
        # Log audit failures but don't break the main operation
        print(f"Failed to log audit event: {e}")

async def get_audit_trail(
    db,
    user_id: Optional[str] = None,
    resource_id: Optional[str] = None,
    action: Optional[AuditAction] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    limit: int = 100,
    skip: int = 0
):
    """Retrieve audit trail records with filtering"""
    
    filter_query = {}
    
    if user_id:
        filter_query["user_id"] = user_id
    
    if resource_id:
        filter_query["resource_id"] = resource_id
    
    if action:
        filter_query["action"] = action.value
    
    if start_date or end_date:
        date_filter = {}
        if start_date:
            date_filter["$gte"] = start_date
        if end_date:
            date_filter["$lte"] = end_date
        filter_query["timestamp"] = date_filter
    
    cursor = db.audit_trail.find(filter_query).sort("timestamp", -1).skip(skip).limit(limit)
    records = await cursor.to_list(length=limit)
    
    return [AuditTrail(**record) for record in records]
