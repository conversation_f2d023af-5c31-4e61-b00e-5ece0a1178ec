from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from enum import Enum

class VerificationStatus(str, Enum):
    FOUND = "found"
    NOT_FOUND = "not_found"
    DAMAGED = "damaged"
    MOVED = "moved"
    EXCEPTION = "exception"

class VerificationEventBase(BaseModel):
    asset_id: str = Field(..., description="Asset identifier")
    verification_date: datetime = Field(default_factory=datetime.utcnow)
    status: VerificationStatus = Field(..., description="Verification status")
    location_found: Optional[str] = Field(None, description="Location where asset was found")
    condition_notes: Optional[str] = Field(None, description="Notes about asset condition")
    exception_reason: Optional[str] = Field(None, description="Reason for exception")
    photos: List[str] = Field(default=[], description="Photos taken during verification")

class VerificationEventCreate(VerificationEventBase):
    pass

class VerificationEvent(VerificationEventBase):
    id: Optional[str] = Field(None, alias="_id")
    verifier_id: str = Field(..., description="ID of user who performed verification")
    verifier_name: str = Field(..., description="Name of verifier")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        populate_by_name = True

class VerificationCampaign(BaseModel):
    id: Optional[str] = Field(None, alias="_id")
    name: str = Field(..., description="Campaign name")
    description: Optional[str] = Field(None, description="Campaign description")
    start_date: datetime = Field(..., description="Campaign start date")
    end_date: datetime = Field(..., description="Campaign end date")
    locations: List[str] = Field(..., description="Locations to verify")
    assigned_verifiers: List[str] = Field(..., description="Assigned verifier IDs")
    status: str = Field(default="planned", description="Campaign status")
    created_by: str = Field(..., description="Creator user ID")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        populate_by_name = True
