from pydantic import BaseModel, Field, EmailStr
from typing import Optional, List
from datetime import datetime
from enum import Enum

class UserRole(str, Enum):
    ADMIN = "admin"
    ASSET_MANAGER = "asset_manager"
    AUDITOR = "auditor"

class UserStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    LOCKED = "locked"
    SUSPENDED = "suspended"

class UserBase(BaseModel):
    username: str = Field(..., min_length=3, max_length=50, description="Username")
    email: str = Field(..., description="Email address")
    full_name: str = Field(..., min_length=2, max_length=100, description="Full name")
    role: UserRole = Field(..., description="User role")
    department: Optional[str] = Field(None, description="Department")
    phone: Optional[str] = Field(None, description="Phone number")

class UserCreate(UserBase):
    password: str = Field(..., min_length=12, description="Password")

class UserUpdate(BaseModel):
    email: Optional[str] = None
    full_name: Optional[str] = None
    role: Optional[UserRole] = None
    department: Optional[str] = None
    phone: Optional[str] = None
    status: Optional[UserStatus] = None

class PasswordChange(BaseModel):
    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., min_length=12, description="New password")

class PasswordReset(BaseModel):
    new_password: str = Field(..., min_length=12, description="New password")

class UserStatusUpdate(BaseModel):
    status: UserStatus = Field(..., description="New user status")
    reason: Optional[str] = Field(None, description="Reason for status change")

class RoleAssignment(BaseModel):
    role: UserRole = Field(..., description="New role to assign")
    reason: Optional[str] = Field(None, description="Reason for role change")

class User(UserBase):
    id: Optional[str] = Field(None, alias="_id")
    status: UserStatus = Field(default=UserStatus.ACTIVE)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    last_login: Optional[datetime] = None
    password_changed_at: datetime = Field(default_factory=datetime.utcnow)
    failed_login_attempts: int = Field(default=0)
    locked_until: Optional[datetime] = None
    password_history: List[str] = Field(default=[])
    
    class Config:
        populate_by_name = True
        json_schema_extra = {
            "example": {
                "username": "john.doe",
                "email": "<EMAIL>",
                "full_name": "John Doe",
                "role": "asset_manager",
                "department": "IT",
                "phone": "+1234567890",
                "status": "active"
            }
        }

class UserInDB(User):
    hashed_password: str

class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: User

class TokenData(BaseModel):
    username: Optional[str] = None
    role: Optional[UserRole] = None
