#!/usr/bin/env python3

import requests
import json

def test_login():
    url = "http://localhost:8000/api/auth/token"
    
    # Test data
    data = {
        "username": "admin",
        "password": "admin123!@#"
    }
    
    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    try:
        print("Testing login API...")
        print(f"URL: {url}")
        print(f"Data: {data}")
        
        response = requests.post(url, data=data, headers=headers, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Body: {response.text}")
        
        if response.status_code == 200:
            print("✅ Login successful!")
            result = response.json()
            print(f"Access Token: {result.get('access_token', 'Not found')}")
            print(f"User: {result.get('user', 'Not found')}")
        else:
            print("❌ Login failed!")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    test_login()
