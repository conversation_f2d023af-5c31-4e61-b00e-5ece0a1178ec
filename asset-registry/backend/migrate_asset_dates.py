#!/usr/bin/env python3
"""
Migration script to add new date fields to existing assets
- maintenance_date: Last maintenance date
- next_maintenance_due: Next maintenance due date  
- disposed_date: Date when asset was disposed
"""

import asyncio
import sys
from datetime import datetime, timedelta
from motor.motor_asyncio import AsyncIOMotorClient

# Database settings
MONGODB_URL = "mongodb://localhost:27017"
DATABASE_NAME = "asset_registry"

async def migrate_asset_dates():
    """Migrate existing assets to include new date fields"""
    
    print("🚀 Starting asset date migration...")
    
    # Connect to MongoDB
    client = AsyncIOMotorClient(MONGODB_URL)
    db = client[DATABASE_NAME]
    
    try:
        # Test connection
        await client.admin.command('ping')
        print("✓ Connected to MongoDB successfully")
        
        # Get all existing assets
        cursor = db.assets.find({})
        assets = await cursor.to_list(length=None)
        
        if not assets:
            print("ℹ️  No assets found in database")
            return
        
        print(f"📊 Found {len(assets)} assets to migrate")
        
        updated_count = 0
        
        for asset in assets:
            asset_id = asset.get('asset_id', 'Unknown')
            status = asset.get('status', 'active')
            purchase_date = asset.get('purchase_date')
            created_at = asset.get('created_at', datetime.utcnow())
            
            # Prepare update fields
            update_fields = {}
            
            # Add maintenance_date if not exists
            if 'maintenance_date' not in asset:
                # Set maintenance date based on asset age and status
                if status in ['maintenance', 'under_maintenance']:
                    # If asset is currently in maintenance, set maintenance date to recent
                    maintenance_date = datetime.utcnow() - timedelta(days=30)
                elif purchase_date:
                    # Set maintenance date to 6 months after purchase for older assets
                    if isinstance(purchase_date, str):
                        purchase_dt = datetime.fromisoformat(purchase_date.replace('Z', '+00:00'))
                    else:
                        purchase_dt = purchase_date
                    maintenance_date = purchase_dt + timedelta(days=180)
                else:
                    # Default to 6 months after creation
                    maintenance_date = created_at + timedelta(days=180)
                
                update_fields['maintenance_date'] = maintenance_date
            
            # Add next_maintenance_due if not exists
            if 'next_maintenance_due' not in asset:
                maintenance_date = update_fields.get('maintenance_date') or asset.get('maintenance_date')
                if maintenance_date:
                    # Set next maintenance to 1 year after last maintenance
                    if isinstance(maintenance_date, str):
                        maintenance_dt = datetime.fromisoformat(maintenance_date.replace('Z', '+00:00'))
                    else:
                        maintenance_dt = maintenance_date
                    next_maintenance = maintenance_dt + timedelta(days=365)
                else:
                    # Default to 1 year from now
                    next_maintenance = datetime.utcnow() + timedelta(days=365)
                
                update_fields['next_maintenance_due'] = next_maintenance
            
            # Add disposed_date if not exists
            if 'disposed_date' not in asset:
                if status == 'disposed':
                    # If asset is disposed, set disposed date to recent past
                    disposed_date = datetime.utcnow() - timedelta(days=60)
                    update_fields['disposed_date'] = disposed_date
                else:
                    # For non-disposed assets, set to null
                    update_fields['disposed_date'] = None
            
            # Update the asset if there are fields to update
            if update_fields:
                await db.assets.update_one(
                    {"_id": asset["_id"]},
                    {"$set": update_fields}
                )
                updated_count += 1
                print(f"✓ Updated asset {asset_id} with new date fields")
        
        print(f"\n🎉 Migration completed successfully!")
        print(f"📊 Updated {updated_count} assets with new date fields")
        
        # Show summary of what was added
        print("\n📋 Summary of changes:")
        print("   - maintenance_date: Last maintenance performed")
        print("   - next_maintenance_due: Next scheduled maintenance")
        print("   - disposed_date: Date when asset was disposed (null for active assets)")
        
    except Exception as e:
        print(f"❌ Error during migration: {e}")
        sys.exit(1)
    finally:
        client.close()
        print("🔌 Disconnected from MongoDB")

if __name__ == "__main__":
    asyncio.run(migrate_asset_dates())
