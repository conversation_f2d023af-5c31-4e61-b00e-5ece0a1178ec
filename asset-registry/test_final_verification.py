#!/usr/bin/env python3
"""
Final test to verify the ObjectId serialization fix is working
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
USERNAME = "admin"
PASSWORD = "AdminPass123!@#"

def test_verification_status_complete():
    """Complete test of the verification status endpoint"""
    
    print("🧪 Final Verification Status Test")
    print("=" * 50)
    
    # Step 1: Login
    print("🔐 Logging in...")
    login_data = {"username": USERNAME, "password": PASSWORD}
    
    response = requests.post(f"{BASE_URL}/api/auth/token", data=login_data)
    if response.status_code != 200:
        print(f"❌ Login failed: {response.status_code} - {response.text}")
        return False
    
    token_data = response.json()
    token = token_data.get("access_token")
    if not token:
        print("❌ No access token received")
        return False
    
    print("✅ Login successful")
    
    # Step 2: Test verification status endpoint
    print("📊 Testing verification status endpoint...")
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(f"{BASE_URL}/api/assets/verification-status", headers=headers)
    
    if response.status_code == 200:
        print("✅ Verification status endpoint working!")
        
        try:
            data = response.json()
            print("✅ JSON response parsed successfully (ObjectId serialization fixed!)")
            
            # Print summary
            summary = data.get("summary", {})
            print(f"📈 Summary:")
            print(f"   Total Assets: {summary.get('total_assets', 0)}")
            print(f"   Active Verifications: {summary.get('active_verifications', 0)}")
            print(f"   Overdue Verifications: {summary.get('overdue_verifications', 0)}")
            print(f"   Upcoming Verifications: {summary.get('upcoming_verifications', 0)}")
            print(f"   Exceptions: {summary.get('exceptions', 0)}")
            
            # Test data structure
            required_keys = ["summary", "active_verifications", "overdue_verifications", "upcoming_verifications", "exceptions"]
            for key in required_keys:
                if key in data:
                    print(f"✅ {key} present in response")
                else:
                    print(f"❌ {key} missing from response")
                    return False
            
            return True
            
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing failed: {e}")
            print(f"Raw response: {response.text}")
            return False
            
    else:
        print(f"❌ Verification status endpoint failed: {response.status_code}")
        print(f"Response: {response.text}")
        return False

if __name__ == "__main__":
    try:
        success = test_verification_status_complete()
        if success:
            print("\n🎉 ALL TESTS PASSED!")
            print("✅ ObjectId serialization error has been FIXED!")
            print("✅ Verification status functionality is working correctly!")
            print("✅ Asset Manager can now access verification status without errors!")
        else:
            print("\n❌ Tests failed. Please check the logs above.")
    except Exception as e:
        print(f"\n💥 Test failed with exception: {e}")
