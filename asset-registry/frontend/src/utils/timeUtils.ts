/**
 * Utility functions for time formatting and manipulation
 */

/**
 * Get relative time string (e.g., "2 hours ago", "Just now")
 */
export const getRelativeTime = (timestamp: string | Date): string => {
  const now = new Date();
  const time = new Date(timestamp);
  const diffInSeconds = Math.floor((now.getTime() - time.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'Just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} day${days > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 2592000) {
    const weeks = Math.floor(diffInSeconds / 604800);
    return `${weeks} week${weeks > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 31536000) {
    const months = Math.floor(diffInSeconds / 2592000);
    return `${months} month${months > 1 ? 's' : ''} ago`;
  } else {
    const years = Math.floor(diffInSeconds / 31536000);
    return `${years} year${years > 1 ? 's' : ''} ago`;
  }
};

/**
 * Format timestamp for display with both absolute and relative time
 */
export const formatTimestamp = (timestamp: string | Date): { absolute: string; relative: string } => {
  const date = new Date(timestamp);
  return {
    absolute: date.toLocaleString(),
    relative: getRelativeTime(timestamp)
  };
};

/**
 * Format date only (without time)
 */
export const formatDate = (timestamp: string | Date): string => {
  const date = new Date(timestamp);
  return date.toLocaleDateString();
};

/**
 * Format time only (without date)
 */
export const formatTime = (timestamp: string | Date): string => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString();
};

/**
 * Check if timestamp is within the last 24 hours
 */
export const isWithinLast24Hours = (timestamp: string | Date): boolean => {
  const now = new Date();
  const time = new Date(timestamp);
  const diffInHours = (now.getTime() - time.getTime()) / (1000 * 60 * 60);
  return diffInHours <= 24;
};

/**
 * Check if timestamp is within the last week
 */
export const isWithinLastWeek = (timestamp: string | Date): boolean => {
  const now = new Date();
  const time = new Date(timestamp);
  const diffInDays = (now.getTime() - time.getTime()) / (1000 * 60 * 60 * 24);
  return diffInDays <= 7;
};

/**
 * Get time ago with more precise formatting
 */
export const getDetailedTimeAgo = (timestamp: string | Date): string => {
  const now = new Date();
  const time = new Date(timestamp);
  const diffInSeconds = Math.floor((now.getTime() - time.getTime()) / 1000);

  if (diffInSeconds < 30) {
    return 'Just now';
  } else if (diffInSeconds < 60) {
    return `${diffInSeconds} seconds ago`;
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    const seconds = diffInSeconds % 60;
    if (minutes < 5) {
      return `${minutes} minute${minutes > 1 ? 's' : ''} ${seconds} second${seconds !== 1 ? 's' : ''} ago`;
    }
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    const minutes = Math.floor((diffInSeconds % 3600) / 60);
    if (hours < 6) {
      return `${hours} hour${hours > 1 ? 's' : ''} ${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
    }
    return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  } else {
    return getRelativeTime(timestamp);
  }
};

/**
 * Format timestamp for audit logs with enhanced detail
 */
export const formatAuditTimestamp = (timestamp: string | Date): { 
  primary: string; 
  secondary: string; 
  tooltip: string 
} => {
  const date = new Date(timestamp);
  const isRecent = isWithinLast24Hours(timestamp);
  
  return {
    primary: date.toLocaleString(),
    secondary: isRecent ? getDetailedTimeAgo(timestamp) : getRelativeTime(timestamp),
    tooltip: `${date.toLocaleDateString()} at ${date.toLocaleTimeString()}`
  };
};
