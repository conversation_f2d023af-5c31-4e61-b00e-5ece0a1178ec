import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Bar, Doughnut } from 'react-chartjs-2';
import { Box, Paper, Typography } from '@mui/material';
import { DashboardStats } from '../types';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface AssetChartsProps {
  stats: DashboardStats;
}

const AssetCharts: React.FC<AssetChartsProps> = ({ stats }) => {
  // Bar Chart Data for Asset Status
  const barChartData = {
    labels: ['Total Assets', 'Active Assets', 'Maintenance Assets', 'Disposed Assets'],
    datasets: [
      {
        label: 'Number of Assets',
        data: [
          stats.total_assets,
          stats.active_assets,
          stats.maintenance_assets,
          stats.disposed_assets,
        ],
        backgroundColor: [
          'rgba(54, 162, 235, 0.8)',   // Blue for Total
          'rgba(75, 192, 192, 0.8)',   // Green for Active
          'rgba(255, 206, 86, 0.8)',   // Yellow for Maintenance
          'rgba(255, 99, 132, 0.8)',   // Red for Disposed
        ],
        borderColor: [
          'rgba(54, 162, 235, 1)',
          'rgba(75, 192, 192, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(255, 99, 132, 1)',
        ],
        borderWidth: 2,
      },
    ],
  };

  // Doughnut Chart Data for Asset Distribution
  const doughnutChartData = {
    labels: ['Active Assets', 'Maintenance Assets', 'Disposed Assets', 'Other'],
    datasets: [
      {
        label: 'Asset Distribution',
        data: [
          stats.active_assets,
          stats.maintenance_assets,
          stats.disposed_assets,
          stats.total_assets - stats.active_assets - stats.maintenance_assets - stats.disposed_assets,
        ],
        backgroundColor: [
          'rgba(75, 192, 192, 0.8)',   // Green for Active
          'rgba(255, 206, 86, 0.8)',   // Yellow for Maintenance
          'rgba(255, 99, 132, 0.8)',   // Red for Disposed
          'rgba(201, 203, 207, 0.8)',  // Gray for Other
        ],
        borderColor: [
          'rgba(75, 192, 192, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(255, 99, 132, 1)',
          'rgba(201, 203, 207, 1)',
        ],
        borderWidth: 2,
      },
    ],
  };

  const barChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Asset Status Overview',
        font: {
          size: 16,
          weight: 'bold' as const,
        },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          stepSize: 1,
        },
      },
    },
  };

  const doughnutChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'bottom' as const,
      },
      title: {
        display: true,
        text: 'Asset Distribution',
        font: {
          size: 16,
          weight: 'bold' as const,
        },
      },
    },
  };

  return (
    <Box sx={{ mt: 3 }}>
      <Typography variant="h5" gutterBottom sx={{ mb: 3, fontWeight: 'bold' }}>
        Asset Analytics
      </Typography>
      
      <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '2fr 1fr' }, gap: 3, mb: 3 }}>
        {/* Bar Chart */}
        <Paper sx={{ p: 3, height: '400px' }}>
          <Bar data={barChartData} options={barChartOptions} />
        </Paper>

        {/* Doughnut Chart */}
        <Paper sx={{ p: 3, height: '400px' }}>
          <Doughnut data={doughnutChartData} options={doughnutChartOptions} />
        </Paper>
      </Box>

      <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' }, gap: 3 }}>
        {/* Asset Value Chart */}
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
            Asset Value Overview
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="body1">Total Value:</Typography>
              <Typography variant="h6" sx={{ color: 'primary.main', fontWeight: 'bold' }}>
                ${stats.total_value?.toLocaleString() || 0}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="body1">Current Value:</Typography>
              <Typography variant="h6" sx={{ color: 'success.main', fontWeight: 'bold' }}>
                ${stats.current_value?.toLocaleString() || 0}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="body1">Depreciated Value:</Typography>
              <Typography variant="h6" sx={{ color: 'error.main', fontWeight: 'bold' }}>
                ${stats.depreciated_value?.toLocaleString() || 0}
              </Typography>
            </Box>
          </Box>
        </Paper>

        {/* Quick Stats */}
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
            Quick Statistics
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="body1">Pending Verification:</Typography>
              <Typography variant="h6" sx={{ color: 'warning.main', fontWeight: 'bold' }}>
                {stats.pending_verification || 0}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="body1">Maintenance Due:</Typography>
              <Typography variant="h6" sx={{ color: 'error.main', fontWeight: 'bold' }}>
                {stats.maintenance_due || 0}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="body1">WIP Assets:</Typography>
              <Typography variant="h6" sx={{ color: 'info.main', fontWeight: 'bold' }}>
                {stats.wip_assets || 0}
              </Typography>
            </Box>
          </Box>
        </Paper>
      </Box>
    </Box>
  );
};

export default AssetCharts;
